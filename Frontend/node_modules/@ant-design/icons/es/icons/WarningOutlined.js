import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import WarningOutlinedSvg from "@ant-design/icons-svg/es/asn/WarningOutlined";
import AntdIcon from "../components/AntdIcon";
var WarningOutlined = function WarningOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: WarningOutlinedSvg
  }));
};

/**![warning](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ2NCA3MjBhNDggNDggMCAxMDk2IDAgNDggNDggMCAxMC05NiAwem0xNi0zMDR2MTg0YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LThWNDE2YzAtNC40LTMuNi04LTgtOGgtNDhjLTQuNCAwLTggMy42LTggOHptNDc1LjcgNDQwbC00MTYtNzIwYy02LjItMTAuNy0xNi45LTE2LTI3LjctMTZzLTIxLjYgNS4zLTI3LjcgMTZsLTQxNiA3MjBDNTYgODc3LjQgNzEuNCA5MDQgOTYgOTA0aDgzMmMyNC42IDAgNDAtMjYuNiAyNy43LTQ4em0tNzgzLjUtMjcuOUw1MTIgMjM5LjlsMzM5LjggNTg4LjJIMTcyLjJ6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(WarningOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'WarningOutlined';
}
export default RefIcon;