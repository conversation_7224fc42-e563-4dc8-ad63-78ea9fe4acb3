FROM tiangolo/uvicorn-gunicorn-fastapi:python3.11

# Clear cache first, then install packages in batches to manage disk space
RUN rm -rf /var/cache/apt/archives/* /var/lib/apt/lists/* && \
    apt-get update --allow-insecure-repositories && \
    apt-get install -y --allow-unauthenticated --no-install-recommends wget gcc g++ && \
    apt-get clean && rm -rf /var/cache/apt/archives/* && \
    apt-get install -y --allow-unauthenticated --no-install-recommends ghostscript && \
    apt-get clean && rm -rf /var/cache/apt/archives/* && \
    apt-get install -y --allow-unauthenticated --no-install-recommends python3-tk libgl1-mesa-glx && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/*

RUN python3 -m pip install --user poetry
ENV PATH="/root/.local/bin:$PATH"

WORKDIR /app/backend/

COPY . .

RUN poetry config installer.max-workers 10
RUN poetry config virtualenvs.create false
RUN poetry install --no-root

ENV PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python

CMD ["uvicorn", "main:app", "--host=0.0.0.0", "--port=80", "--reload"]
