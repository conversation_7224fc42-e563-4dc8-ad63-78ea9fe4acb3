FROM tiangolo/uvicorn-gunicorn-fastapi:python3.11

# Update package lists and install required packages
RUN apt-get update && \
    apt-get install -y wget gcc g++ ghostscript python3-tk libgl1-mesa-glx && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

RUN python3 -m pip install --user poetry
ENV PATH="/root/.local/bin:$PATH"

WORKDIR /app/backend/

COPY . .

RUN poetry config installer.max-workers 10
RUN poetry config virtualenvs.create false
RUN poetry install --no-root

ENV PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python

CMD ["uvicorn", "main:app", "--host=0.0.0.0", "--port=80", "--reload"]
