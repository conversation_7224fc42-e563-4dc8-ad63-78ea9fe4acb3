FROM tiangolo/uvicorn-gunicorn-fastapi:python3.11

# Add --allow-insecure-repositories flag to bypass GPG verification
# Clean up first to free space, then install packages with minimal footprint
RUN rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/* /tmp/* /var/tmp/* && \
    apt-get update --allow-insecure-repositories && \
    apt-get install -y --no-install-recommends --allow-unauthenticated \
        gcc g++ wget && \
    apt-get clean && rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/* && \
    apt-get update --allow-insecure-repositories && \
    apt-get install -y --no-install-recommends --allow-unauthenticated \
        ghostscript python3-tk libgl1-mesa-glx && \
    apt-get autoremove -y && \
    apt-get autoclean && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    rm -rf /var/cache/apt/archives/* && \
    rm -rf /tmp/* && \
    rm -rf /var/tmp/*

RUN python3 -m pip install --user poetry
ENV PATH="/root/.local/bin:$PATH"

WORKDIR /app/backend/

COPY . .

RUN poetry config installer.max-workers 10
RUN poetry config virtualenvs.create false
RUN poetry install --no-root

ENV PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python

CMD ["uvicorn", "main:app", "--host=0.0.0.0", "--port=80", "--reload"]
