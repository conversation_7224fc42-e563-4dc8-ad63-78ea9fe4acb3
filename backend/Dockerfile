FROM tiangolo/uvicorn-gunicorn-fastapi:python3.11

# Add --allow-insecure-repositories flag to bypass GPG verification
RUN apt-get update --allow-insecure-repositories && apt-get install -y --allow-unauthenticated wget gcc g++
RUN apt-get install -y --allow-unauthenticated ghostscript python3-tk libgl1-mesa-glx

RUN python3 -m pip install --user poetry
ENV PATH="/root/.local/bin:$PATH"

WORKDIR /app/backend/

COPY . .

RUN poetry config installer.max-workers 10
RUN poetry config virtualenvs.create false
RUN poetry install --no-root

ENV PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python

CMD ["uvicorn", "main:app", "--host=0.0.0.0", "--port=80", "--reload"]
